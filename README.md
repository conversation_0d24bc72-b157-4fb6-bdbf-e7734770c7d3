# Java HTTP Browser

A simple Java GUI application that allows you to make HTTP requests and view the full response including headers.

## Features

- Simple and clean GUI built with Java Swing
- URL input field with automatic protocol detection (adds http:// if missing)
- "Go" button to make HTTP requests
- Large text area displaying the complete HTTP response:
  - Request details (URL, method)
  - Response status code and message
  - All response headers
  - Response body
- Non-blocking UI using SwingWorker
- Default example URL (httpbin.org/get) for testing

## Prerequisites

- Java 8 or higher
- Maven 3.6 or higher

## How to Build and Run

### Method 1: Using Shell Scripts (Easiest)

1. **Build and run in one step:**
   ```bash
   ./run.sh
   ```

2. **Or build first, then run:**
   ```bash
   ./build.sh
   java -cp build/classes com.javabrowser.HttpBrowserApp
   ```

### Method 2: Using Maven (Recommended for development)

1. **Build the project:**
   ```bash
   mvn clean compile
   ```

2. **Run the application:**
   ```bash
   mvn exec:java
   ```

### Method 3: Create and Run Executable JAR

1. **Build the executable JAR:**
   ```bash
   mvn clean package
   ```

2. **Run the JAR file:**
   ```bash
   java -jar target/java-browser-1.0.0.jar
   ```

### Method 4: Manual Compilation (if Maven is not available)

1. **Create the directory structure and compile:**
   ```bash
   mkdir -p build/classes
   javac -d build/classes src/main/java/com/javabrowser/HttpBrowserApp.java
   ```

2. **Run the application:**
   ```bash
   java -cp build/classes com.javabrowser.HttpBrowserApp
   ```

## Usage

1. Launch the application using one of the methods above
2. Enter a URL in the text field (e.g., `https://httpbin.org/get`, `google.com`, `api.github.com`)
3. Click the "Go" button or press Enter
4. View the complete HTTP response in the text area below

## Example URLs to Test

- `https://httpbin.org/get` - Returns JSON with request information
- `https://httpbin.org/headers` - Returns your request headers
- `https://api.github.com/users/octocat` - GitHub API example
- `google.com` - Will be converted to `http://google.com`
- `https://jsonplaceholder.typicode.com/posts/1` - Sample JSON API

## Application Structure

```
java-browser/
├── src/main/java/com/javabrowser/
│   └── HttpBrowserApp.java          # Main application class
├── build.sh                         # Build script
├── run.sh                          # Run script
├── pom.xml                          # Maven configuration
└── README.md                        # This file
```

## Technical Details

- **GUI Framework:** Java Swing
- **HTTP Client:** Java's built-in HttpURLConnection
- **Threading:** SwingWorker for non-blocking HTTP requests
- **Build Tool:** Maven
- **Java Version:** Compatible with Java 8+

## Troubleshooting

- **"Please enter a URL" message:** Make sure you've entered a URL in the input field
- **Connection timeout:** The application has a 10-second timeout for connections
- **SSL/HTTPS issues:** Some older Java versions may have issues with certain SSL certificates
- **Look and Feel:** The application attempts to use your system's native look and feel

## License

This is a simple educational project. Feel free to use and modify as needed.
