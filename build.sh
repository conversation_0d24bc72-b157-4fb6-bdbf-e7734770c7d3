#!/bin/bash

echo "Building Java HTTP Browser..."

# Create build directory
mkdir -p build/classes

# Compile the Java source
echo "Compiling Java source..."
javac -d build/classes src/main/java/com/javabrowser/HttpBrowserApp.java

if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    echo "You can now run the application with:"
    echo "  java -cp build/classes com.javabrowser.HttpBrowserApp"
    echo ""
    echo "Or use Maven:"
    echo "  mvn exec:java"
else
    echo "Compilation failed!"
    exit 1
fi
