package com.javabrowser;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;

public class HttpBrowserApp extends JFrame {
    private JTextField urlField;
    private JTextArea responseArea;
    private JButton goButton;
    private JScrollPane scrollPane;

    public HttpBrowserApp() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setTitle("Simple Java HTTP Browser");
        setSize(800, 600);
        setLocationRelativeTo(null);
    }

    private void initializeComponents() {
        urlField = new JTextField(50);
        urlField.setText("https://httpbin.org/get");
        
        goButton = new JButton("Go");
        
        responseArea = new JTextArea();
        responseArea.setEditable(false);
        responseArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        responseArea.setBackground(Color.WHITE);
        
        scrollPane = new JScrollPane(responseArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
    }

    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Top panel for URL input and Go button
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JLabel urlLabel = new JLabel("URL: ");
        topPanel.add(urlLabel, BorderLayout.WEST);
        topPanel.add(urlField, BorderLayout.CENTER);
        topPanel.add(goButton, BorderLayout.EAST);
        
        add(topPanel, BorderLayout.NORTH);
        add(scrollPane, BorderLayout.CENTER);
    }

    private void setupEventHandlers() {
        goButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                makeHttpRequest();
            }
        });
        
        // Allow Enter key to trigger the request
        urlField.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                makeHttpRequest();
            }
        });
    }

    private void makeHttpRequest() {
        String urlString = urlField.getText().trim();
        
        if (urlString.isEmpty()) {
            responseArea.setText("Please enter a URL");
            return;
        }
        
        // Add http:// if no protocol is specified
        if (!urlString.startsWith("http://") && !urlString.startsWith("https://")) {
            urlString = "http://" + urlString;
        }
        
        responseArea.setText("Loading...");
        goButton.setEnabled(false);
        
        // Use SwingWorker to avoid blocking the UI thread
        SwingWorker<String, Void> worker = new SwingWorker<String, Void>() {
            @Override
            protected String doInBackground() throws Exception {
                return performHttpRequest(urlString);
            }
            
            @Override
            protected void done() {
                try {
                    String response = get();
                    responseArea.setText(response);
                    responseArea.setCaretPosition(0); // Scroll to top
                } catch (Exception e) {
                    responseArea.setText("Error: " + e.getMessage());
                }
                goButton.setEnabled(true);
            }
        };
        
        worker.execute();
    }

    private String performHttpRequest(String urlString) throws IOException {
        StringBuilder response = new StringBuilder();
        
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // Set request properties
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000); // 10 seconds
        connection.setReadTimeout(10000); // 10 seconds
        connection.setRequestProperty("User-Agent", "Java HTTP Browser 1.0");
        
        try {
            // Get response code and message
            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();
            
            response.append("=== REQUEST ===\n");
            response.append("URL: ").append(urlString).append("\n");
            response.append("Method: GET\n\n");
            
            response.append("=== RESPONSE ===\n");
            response.append("Status: ").append(responseCode).append(" ").append(responseMessage).append("\n\n");
            
            // Get response headers
            response.append("=== HEADERS ===\n");
            Map<String, List<String>> headers = connection.getHeaderFields();
            for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                String key = entry.getKey();
                if (key == null) key = "Status";
                for (String value : entry.getValue()) {
                    response.append(key).append(": ").append(value).append("\n");
                }
            }
            
            response.append("\n=== BODY ===\n");
            
            // Read response body
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
            
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            reader.close();
            
        } finally {
            connection.disconnect();
        }
        
        return response.toString();
    }

    public static void main(String[] args) {
        // Set look and feel to system default
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            // Use default look and feel if system L&F is not available
        }
        
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new HttpBrowserApp().setVisible(true);
            }
        });
    }
}
